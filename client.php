<?php
require_once 'utils.php';
require_once('mailsender.php');
require_once('alerts.php');
require_once('email_services.php');
require_once('clientmethods/CoinDetailMethods.php');
require_once('clientmethods/IdoDetailMethods.php');
require_once('clientmethods/WatchlistMethods.php');
require_once('clientmethods/IdoWatchlistMethods.php');
require_once('clientmethods/ProfileImageMethods.php');
require_once('metricSpecialProcessors.php');
// Apply CORS headers
cors_client();
$is_debug = true;
//For security purposes, we can allow only specified agent type
$user_agent = $_SERVER['HTTP_USER_AGENT'];
if (preg_match('/python/i', $user_agent)) {
    echo 'You are forbidden!';
    require_once 'stripe/StripeLogger.php';
    foreach ($_POST as $key => $value) {
        echo "a";
        StripeLogger::log(StripeLogLevel::WARNING, "Python Script Request blocked", [
            'key' => $key,
            'value' => $value,
            'user_agent' => $user_agent
        ]);
    }
    header('HTTP/1.0 403 Forbidden');
    return;
}
//$uri = $_SERVER['REQUEST_URI'];   // /api/users
//$method = $_SERVER['REQUEST_METHOD'];  // GET,POST,DELETE, etc.
include_once('config.php');
$payload = file_get_contents('php://input');
$data = json_decode($payload, true);
if ($data == null) {
    $debug_info = "";
    if ($is_debug)
        $debug_info = "Payload is not a valid json or null";
    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;
}
$functName = "";
if (isset($data['f'])) {
    $functName = $data['f'];
} else {
    $debug_info = "";
    if ($is_debug)
        $debug_info = "Endpoint variable is not set";
    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;
}
/* ============================================================================================== */
/*                                        language options                                        */
/* ============================================================================================== */
// Import language configuration
require_once 'language_config.php';
$selectedLanguage = getSelectedLanguage();
// Import language strings
require_once 'language_strings.php';
/* ============================================================================================== */
/*                                         endpoint arrays                                        */
/* ============================================================================================== */
$validEndpoints = array(
    "get_coins_list",
    "get_coins_list_v2",
    "get_coins_search_list",
    "get_metric_texts_by_id",
    "get_current_price_by_coin_id",
    "get_categories",
    "get_chains",
    "sbc_optns",
    "get_dashboard_highlights",
    "get_upcoming_idos",
    "get_upcoming_idos_v2",
    "contact_support_request",
    "get_documentation",
);
$authorizedEndpoints = array(
    "retrieve_current_user",
    "get_coin_detail_by_id",
    "get_adv_ido_detail_by_id",
    "get_ido_detail_by_id",
    "get_coin_summary",
    "get_adv_coin_detail_by_id",
    "get_user_watchlists",
    "get_user_portfolios",
    "get_user_notifications",
    "get_notifications",
    "mark_notification_read",
    "get_unread_notifications_count",
    "get_notifications_since",
    "add_to_watchlist",
    "add_to_watchlist_v2",
    "remove_from_watchlist_v2",
    "add_new_watchlist",
    "delete_watchlist",
    "edit_watchlist",
    "get_user_ido_watchlists",
    "add_to_ido_watchlist",
    "remove_from_ido_watchlist",
    "add_new_ido_watchlist",
    "delete_ido_watchlist",
    "edit_ido_watchlist",
    "report_error",
    "report_error_v2",
    "request_feature",
    "search_assets",
    "search_coins",
    "create_alert",
    "get_user_alerts",
    "delete_alert",
    "reset_alert_triggers",
    "get_profile_image",
    "update_user_avatar",
    "remove_user_avatar",
    "compare_coins",
    "get_launchpad_platforms"
);
if (in_array($functName, $validEndpoints)) {
    if ($functName == 'get_coins_list') {
        $functName($data['marketcap_min'], $data['marketcap_max'], $data['score_min'], $data['score_max'], $data['categories'], $data['chains']);
    } else if ($functName == 'get_coins_list_v2') {
        $functName(
            $data['marketcap_min'],
            $data['marketcap_max'],
            $data['score_min'],
            $data['score_max'],
            $data['categories'],
            $data['chains'],
            $data['change'],
            $data["newly_listed"],
            $data["listing_date"] ?? null,
            $data["listing_date_start"] ?? null,
            $data["listing_date_end"] ?? null
        );
    } else if ($functName == 'get_coins_search_list') {
        $functName();
    } else if ($functName == 'get_metric_texts_by_id') {
        $functName($data['id'], $data['coin_id'] ?? null);
    } else if ($functName == 'get_current_price_by_coin_id') {
        $functName($data['coin_id']);
    } else if ($functName == "get_categories") {
        $functName();
    } else if ($functName == "get_chains") {
        $functName();
    } else if ($functName == "sbc_optns") {
        $functName();
    } else if ($functName == "get_dashboard_highlights") {
        $functName();
    } else if ($functName == "get_upcoming_idos") {
        $functName($userid);
    } else if ($functName == "get_upcoming_idos_v2") {
        $functName();
    } else if ($functName == "contact_support_request") {
        $functName($data['email'], $data['subject'], $data['message']);
    } else if ($functName == "get_documentation") {
        $functName();
    }
} else if (in_array($functName, $authorizedEndpoints)) {
    $userid = authenticate_user();
    if ($userid) {
        $userPermissions = getUserPermissions($userid);
        if ($functName == 'get_coin_detail_by_id') {
            $functName($data['id']);
        } else if ($functName == 'get_adv_ido_detail_by_id') {
            $functName($data['id']);
        } else if ($functName == 'get_ido_detail_by_id') {
            $functName($data['id']);
        } else if ($functName == 'get_coin_summary') {
            $functName($data['id']);
        } else if ($functName == 'get_adv_coin_detail_by_id') {
            $functName($data['id']);
        } else if ($functName == "get_user_watchlists") {
            $functName($userid);
        } else if ($functName == "get_user_ido_watchlists") {
            $functName($userid);
        } else if ($functName == "get_user_notifications") {
            $functName($userid);
        } else if ($functName == "get_notifications") {
            get_user_notifications_from_db($userid);
        } else if ($functName == "mark_notification_read") {
            mark_notification_read($userid, $data['notification_id']);
        } else if ($functName == "get_unread_notifications_count") {
            get_unread_notifications_count($userid);
        } else if ($functName == "get_notifications_since") {
            get_notifications_since($userid, $data['since'] ?? null);
        } else if ($functName == "get_user_portfolios") {
            $functName($userid);
        } else if ($functName == "add_to_watchlist") {
            $functName($data['listId'], $data['coinId']);
        } else if ($functName == "add_to_watchlist_v2") {
            $functName($data['listId'], $data['coinId']);
        } else if ($functName == "remove_from_watchlist_v2") {
            $functName($data['listId'], $data['coinId']);
        } else if ($functName == "add_new_watchlist") {
            $functName($userid, $data['name'], $data['iconid'], $data['coinid'], $data['description']);
        } else if ($functName == "delete_watchlist") {
            $functName($data['listId'], $userid);
        } else if ($functName == "edit_watchlist") {
            $functName($data['id'], $data['name'], $data['icon_id'], $userid, $data['description']);
        } else if ($functName == "add_to_ido_watchlist") {
            $functName($data['listId'], $data['idoId']);
        } else if ($functName == "remove_from_ido_watchlist") {
            $functName($data['listId'], $data['idoId']);
        } else if ($functName == "add_new_ido_watchlist") {
            $functName($userid, $data['name'], $data['iconid'], $data['projectid'], $data['description']);
        } else if ($functName == "delete_ido_watchlist") {
            $functName($data['listId'], $userid);
        } else if ($functName == "edit_ido_watchlist") {
            $functName($data['id'], $data['name'], $data['iconid'], $userid, $data['description']);
        } else if ($functName == "report_error") {
            $functName($data['info'], $data['detail']);
        } else if ($functName == "report_error_v2") {
            $id = $data['coin_id'] ?? $data['ido_id'] ?? null;
            $name = $data['coin_name'] ?? $data['ido_name'] ?? null;
            $functName($id, $name, $data['detail'], $userid);
        } else if ($functName == "request_feature") {
            $functName($data['detail'], $userid);
        } else if ($functName == "retrieve_current_user") {
            $functName($userid);
        } else if ($functName == "search_assets") {
            $functName($data['query']);
        } else if ($functName == "search_coins") {
            $functName($data['query']);
        } else if ($functName == "create_alert") {
            $functName(
                $userid,
                $data['coin'],
                $data['type'],
                $data['notificationType'],
                $data['conditionAbove'] ?? false,
                $data['conditionBelow'] ?? false,
                $data['thresholdAbove'] ?? null,
                $data['thresholdBelow'] ?? null,
                $data['priceAbove'] ?? null,
                $data['priceBelow'] ?? null
            );
        } else if ($functName == "get_user_alerts") {
            $functName($userid);
        } else if ($functName == "delete_alert") {
            $functName($userid, $data['alertId']);
        } else if ($functName == "reset_alert_triggers") {
            $functName($userid, $data['alertId'], $data['conditionType'] ?? 'all');
        } else if ($functName == "get_profile_image") {
            $functName();
        } else if ($functName == "update_user_avatar") {
            $functName($userid, $data['avatar_id']);
        } else if ($functName == "remove_user_avatar") {
            $functName($userid);
        } else if ($functName == "compare_coins") {
            $functName($userid, $data['coin_ids']);
        } else if ($functName == "get_launchpad_platforms") {
            $functName();
        }
    } else {
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
        $response = new ErrorResult($clientMessages[$lang]['unauthorized_access']);
        $response->send(401);
    }
} else {
    global $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    $debug_info = "";
    if ($is_debug)
        $debug_info = "Endpoint Not Exists";
    http_response_code(404);
    echo json_encode(['error' => $clientMessages[$lang]['invalid_request'], 'detail' => $debug_info]);
}
/* ============================================================================================== */
/*                                            endpoints                                           */
/* ============================================================================================== */


function get_user_notifications($userid)
{
    global $selectedLanguage, $clientMessages;
    // Dil desteği için varsayılan dil kontrolü
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    // Test amaçlı statik bildirimler
    $alerts = [
        [
            'id' => '1',
            'title' => $clientMessages[$lang]['bitcoin_price_up'],
            'message' => $clientMessages[$lang]['bitcoin_price_up_message'],
            'type' => 'price',
            'priority' => 'medium',
            'status' => 'unread',
            'timestamp' => date(DATE_ATOM),
            'link' => '/coins/bitcoin',
            'coinId' => 'bitcoin',
            'coinName' => 'Bitcoin',
            'coinSymbol' => 'BTC',
            'data' => ['change' => 5.0]
        ],
        [
            'id' => '2',
            'title' => $clientMessages[$lang]['solana_update'],
            'message' => $clientMessages[$lang]['solana_update_message'],
            'type' => 'update',
            'priority' => 'low',
            'status' => 'unread',
            'timestamp' => date(DATE_ATOM, strtotime('-1 hour')),
            'link' => '/coins/solana',
            'coinId' => 'solana',
            'coinName' => 'Solana',
            'coinSymbol' => 'SOL',
        ],
        [
            'id' => '3',
            'title' => $clientMessages[$lang]['ethereum_system'],
            'message' => $clientMessages[$lang]['ethereum_system_message'],
            'type' => 'system',
            'priority' => 'medium',
            'status' => 'read',
            'timestamp' => date(DATE_ATOM, strtotime('-2 hours')),
            'coinId' => 'ethereum',
            'coinName' => 'Ethereum',
            'coinSymbol' => 'ETH',
        ],
        [
            'id' => '4',
            'title' => $clientMessages[$lang]['security_alert'],
            'message' => $clientMessages[$lang]['security_alert_message'],
            'type' => 'security',
            'priority' => 'high',
            'status' => 'unread',
            'timestamp' => date(DATE_ATOM, strtotime('-30 minutes')),
        ],
        [
            'id' => '5',
            'title' => $clientMessages[$lang]['cardano_price_down'],
            'message' => $clientMessages[$lang]['cardano_price_down_message'],
            'type' => 'price',
            'priority' => 'medium',
            'status' => 'read',
            'timestamp' => date(DATE_ATOM, strtotime('-3 hours')),
            'link' => '/coins/cardano',
            'coinId' => 'cardano',
            'coinName' => 'Cardano',
            'coinSymbol' => 'ADA',
            'data' => ['change' => -3.0]
        ]
    ];
    $response = new SuccessResult($alerts);
    $response->send();
}
function get_user_portfolios($userid)
{
    global $link;
    $userid = intval($userid);
    $query = "SELECT * FROM portfolio WHERE user_id = $userid";
    $result = mysqli_query($link, $query);
    if ($result) {
        $portfolios = mysqli_fetch_all($result, MYSQLI_ASSOC);
        $all_portfolios = []; // Tüm portföyleri toplamak için bir array
        foreach ($portfolios as &$portfolio) {
            $portfolio_id = $portfolio['id'];
            $coin_query = "
            SELECT c.id, c.geckoid, c.name, c.image, c.symbol, c.total_score, c.marketcap_rank
            FROM portfolio_coins pc
            JOIN coindata_all c ON pc.coin_id = c.id
            WHERE pc.portfolio_id = $portfolio_id
            ";
            $rs = mysqli_query($link, $coin_query);
            $coins = [];
            if ($rs) {
                $pos = 1;
                while ($obj2 = mysqli_fetch_assoc($rs)) {
                    $obj2['pos'] = $pos++;
                    $gecko_id = $obj2['geckoid'];
                    $scores_rs = mysqli_query($link, "
                        SELECT metric_group, score
                        FROM coin_group_scores_all
                        WHERE geckoid = '$gecko_id' AND metric_group IN (1, 2, 3, 4, 5, 6)
                    ");
                    if ($scores_rs) {
                        while ($score_row = mysqli_fetch_assoc($scores_rs)) {
                            if ($score_row['metric_group'] == 1) {
                                $obj2['tokenomicsScore'] = floatval($score_row['score']);
                            } elseif ($score_row['metric_group'] == 3) {
                                $obj2['securityScore'] = floatval($score_row['score']);
                            } elseif ($score_row['metric_group'] == 4) {
                                $obj2['socialScore'] = floatval($score_row['score']);
                            } elseif ($score_row['metric_group'] == 5) {
                                $obj2['marketScore'] = floatval($score_row['score']);
                            } elseif ($score_row['metric_group'] == 6) {
                                $obj2['fundamentalsScore'] = floatval($score_row['score']);
                            }
                        }
                    }
                    $obj2['change'] = rand(-99, 100);
                    $coins[] = $obj2;
                }
            }
            $portfolio['coins'] = $coins; // Portfolio'ye coin bilgilerini ekleyin
            $all_portfolios[] = $portfolio; // Portfolio'yu ana array'e ekleyin
        }
        $response = new SuccessResult($all_portfolios); // Tüm portföyleri döndürün
        $response->send();
    } else {
        $response = new ErrorResult('Failed to retrieve portfolios.');
        $response->send(500);
    }
}



function get_current_price_by_coin_id($coin_id)
{
    $current_price = fetch_price_from_coingecko($coin_id);
    $resp = array("status" => "success", "current_price" => $current_price);
    echo json_encode($resp, JSON_PRETTY_PRINT);
}





function sbc_optns()
{
    global $link;
    $query = "SELECT id, name FROM subscriptions";
    $rs = mysqli_query($link, $query);
    $subscriptions = [];
    if ($rs) {
        while ($subscription = mysqli_fetch_assoc($rs)) {
            $subscription_id = $subscription['id'];
            $pricing_query = "
                SELECT id, duration_in_days, price
                FROM subscription_pricings
                WHERE subscription_id = $subscription_id
            ";
            $pricing_rs = mysqli_query($link, $pricing_query);
            $pricing_options = [];
            if ($pricing_rs) {
                while ($pricing = mysqli_fetch_assoc($pricing_rs)) {
                    $pricing_options[] = $pricing;
                }
            }
            $subscription['pricing_options'] = $pricing_options;
            $subscriptions[] = $subscription;
        }
        $result = new SuccessResult($subscriptions);
        $result->send();
        return;
    }
    $error = new ErrorResult("Failed to fetch subscriptions.");
    $error->send();
}
function get_coins_list_v2(
    $marketcap_min,
    $marketcap_max,
    $score_min,
    $score_max,
    $categories,
    $chains,
    $change = null,
    $newly_listed = null,
    $listing_date = null,
    $listing_date_start = null,
    $listing_date_end = null
) {
    global $link;

    // Include StripeLogger for logging
    require_once 'stripe/StripeLogger.php';

    // Get user ID from JWT token (optional - no authentication required)
    $userId = getUserIdFromJWT();
    $subscriptionLevel = 'free'; // Default to free

    // If user is authenticated, get their validated subscription level
    if ($userId) {
        $query = "SELECT u.subscription_level, s.status as stripe_status
                  FROM users u
                  LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                      AND s.status IN ('active', 'trialing')
                  WHERE u.id = ?
                  ORDER BY s.created_at DESC
                  LIMIT 1";
        $stmt = mysqli_prepare($link, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "i", $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);
            $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
            $stripeStatus = $user['stripe_status'];

            // Security check: Only allow paid features if Stripe subscription is active
            $subscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';

            StripeLogger::log(StripeLogLevel::INFO, "get_coins_list_v2 called by authenticated user", [
                'user_id' => $userId,
                'user_subscription_level' => $userSubscriptionLevel,
                'stripe_status' => $stripeStatus,
                'final_subscription_level' => $subscriptionLevel
            ]);
        }
    } else {
        StripeLogger::log(StripeLogLevel::INFO, "get_coins_list_v2 called by unauthenticated user (free access)");
    }

    // Check if free users are trying to use advanced filters
    $isFreeUser = ($subscriptionLevel === 'free');
    if ($isFreeUser) {
        $hasAdvancedFilters = (
            $marketcap_min !== null ||
            $marketcap_max !== null ||
            $score_min !== null ||
            $score_max !== null ||
            !empty($categories) ||
            !empty($chains) ||
            $listing_date !== null ||
            $listing_date_start !== null ||
            $listing_date_end !== null
        );

        if ($hasAdvancedFilters) {
            StripeLogger::log(StripeLogLevel::INFO, "Free user attempted to use advanced filters", [
                'user_id' => $userId,
                'filters_used' => [
                    'marketcap_min' => $marketcap_min,
                    'marketcap_max' => $marketcap_max,
                    'score_min' => $score_min,
                    'score_max' => $score_max,
                    'categories' => !empty($categories),
                    'chains' => !empty($chains),
                    'listing_date' => $listing_date,
                    'listing_date_start' => $listing_date_start,
                    'listing_date_end' => $listing_date_end
                ]
            ]);

            global $selectedLanguage, $clientMessages;
            $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

            $response = new ErrorResult($clientMessages[$lang]['advanced_filters_subscription_required']);
            $response->send(403);
            return;
        }

        StripeLogger::log(StripeLogLevel::DEBUG, "Free user using allowed filters only", [
            'user_id' => $userId,
            'change' => $change,
            'newly_listed' => $newly_listed
        ]);
    }

    $filters = ["isactive = 1"];
    if ($marketcap_min !== null) $filters[] = "marketcap >= $marketcap_min";
    if ($marketcap_max !== null) $filters[] = "marketcap <= $marketcap_max";
    if ($score_min !== null) $filters[] = "total_score >= $score_min";
    if ($score_max !== null) $filters[] = "total_score <= $score_max";
    // Categories filtresi güncellendi
    if (!empty($categories)) {
        $categories_list = implode(",", array_map(fn($c) => intval($c), $categories));
        $filters[] = "geckoid IN (SELECT cr_key FROM cr_coin_tags WHERE tag_id IN ($categories_list))";
    }
    if (!empty($chains)) {
        $chains_list = "'" . implode("','", array_map(fn($c) => mysqli_real_escape_string($link, $c), $chains)) . "'";
        $filters[] = "geckoslug IN (SELECT geckoid FROM coindata_categories WHERE category_name IN ($chains_list))";
    }
    $order_by = "marketcap DESC";
    if ($change === true) {
        $filters[] = "sevenDayChange > 0.1";
        $order_by = "sevenDayChange DESC, name ASC ";
    }
    // Tarih filtreleme mantığı
    // Eğer newly_listed true ise ve listing_date belirtilmemişse, 6 aylık filtreyi kullan
    if ($newly_listed === true) {
        if ($listing_date !== null) {
            // listing_date değeri gün cinsinden (örn: "90" = son 90 gün)
            $days_ago = intval($listing_date);
            if ($days_ago > 0) {
                $filters[] = "coin_age >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL $days_ago DAY))";
                $filters[] = "coin_age <= UNIX_TIMESTAMP(NOW())";
            }
        } else {
            // Varsayılan olarak 6 ay - get_dashboard_highlights ile tutarlı unix timestamp kullanımı
            $filters[] = "coin_age >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 6 MONTH))";
            $filters[] = "coin_age <= UNIX_TIMESTAMP(NOW())";
        }
        $order_by = "coin_age DESC, name ASC";
    }
    // Belirli bir tarih aralığı için filtreleme
    if ($listing_date_start !== null && $listing_date_end !== null) {
        $start_timestamp = intval($listing_date_start);
        $end_timestamp = intval($listing_date_end);
        if ($start_timestamp > 0 && $end_timestamp > 0) {
            $filters[] = "coin_age BETWEEN $start_timestamp AND $end_timestamp";
            $order_by = "coin_age DESC, name ASC";
        }
    }
    $filter_string = implode(" AND ", $filters);

    // For free users, we need to get total count first
    $isFreeUser = ($subscriptionLevel === 'free');
    $totalCount = 0;

    if ($isFreeUser) {
        // Get total count for free users
        $countQuery = "SELECT COUNT(*) as total FROM client_coin_list WHERE $filter_string";
        $countRs = mysqli_query($link, $countQuery);
        if ($countRs) {
            $countRow = mysqli_fetch_assoc($countRs);
            $totalCount = (int)$countRow['total'];

            StripeLogger::log(StripeLogLevel::INFO, "Free user - total coins available", [
                'user_id' => $userId,
                'total_count' => $totalCount
            ]);
        }
    }

    // Main query with limit for free users
    $query = "SELECT id, geckoid, geckoslug, name, image, symbol, total_score, marketcap_rank,
                     tokenomicsScore, securityScore, socialScore, marketScore, fundamentalsScore,
                     sevenDayChange, coin_age
              FROM client_coin_list
              WHERE $filter_string
              ORDER BY $order_by";

    // Add limit for free users
    if ($isFreeUser) {
        $query .= " LIMIT 20";
    }

    $rs = mysqli_query($link, $query);
    if (!$rs) {
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        (new ErrorResult($clientMessages[$lang]['database_query_failed_simple']))->send(500);
        return;
    }

    $coins = [];

    // Add actual coin data
    while ($coin = mysqli_fetch_assoc($rs)) {
        $coins[] = [
            'id' => $coin['id'],
            'gid' => $coin['geckoid'],
            'rank' => (int)$coin['marketcap_rank'],
            'name' => $coin['name'],
            'symbol' => $coin['symbol'],
            'image' => $coin['image'],
            'tokenomics' => format_score($coin['tokenomicsScore']),
            'security' => format_score($coin['securityScore']),
            'social' => format_score($coin['socialScore']),
            'market' => format_score($coin['marketScore']),
            'insights' => format_score($coin['fundamentalsScore']),
            'totalScore' => format_score($coin['total_score']),
            'sevenDayChange' => round($coin['sevenDayChange'], 2),
            'coin_age' => $coin['coin_age']
        ];
    }

    // Prepare meta information for free users
    $meta = null;
    if ($isFreeUser && $totalCount > 20) {
        $remainingCount = $totalCount - 20;
        $meta = [
            'isDemo' => true,
            'count' => $remainingCount
        ];

        StripeLogger::log(StripeLogLevel::INFO, "Added meta information for free user", [
            'user_id' => $userId,
            'remaining_count' => $remainingCount,
            'total_count' => $totalCount
        ]);
    }

    StripeLogger::log(StripeLogLevel::INFO, "get_coins_list_v2 response prepared", [
        'user_id' => $userId,
        'subscription_level' => $subscriptionLevel,
        'is_free_user' => $isFreeUser,
        'coins_returned' => count($coins),
        'has_meta' => $meta !== null
    ]);

    (new SuccessResult($coins, $meta))->send();
}
function retrieve_current_user($userId)
{
    global $link;
    // Include StripeLogger for logging
    require_once 'stripe/StripeLogger.php';

    // Kullanıcıyı veritabanından çekme sorgusu ve güvenli abonelik seviyesi kontrolü
    $query = "SELECT u.id, u.email, u.email_verified, u.avatar_id, u.username, u.stripe_customer_id, u.subscription_level,
                     s.status as stripe_status, s.plan_name
              FROM users u
              LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                  AND s.status IN ('active', 'trialing')
              WHERE u.id = ?
              ORDER BY s.created_at DESC
              LIMIT 1";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        if ($user) {
            // Security check: Validate subscription level with Stripe status
            $userSubscriptionLevel = strtolower($user['subscription_level'] ?? 'free');
            $stripeStatus = $user['stripe_status'];
            $planName = $user['plan_name'];

            // If user has paid subscription level but no active Stripe subscription, downgrade to free
            if ($userSubscriptionLevel !== 'free' && empty($stripeStatus)) {
                StripeLogger::log(StripeLogLevel::WARNING, "SECURITY: User profile request - paid subscription level but no active Stripe subscription", [
                    'user_id' => $userId,
                    'user_subscription_level' => $userSubscriptionLevel,
                    'stripe_status' => $stripeStatus,
                    'plan_name' => $planName
                ]);

                // Update user subscription level to free for security
                $updateQuery = "UPDATE users SET subscription_level = 'free' WHERE id = ?";
                $updateStmt = mysqli_prepare($link, $updateQuery);
                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "i", $userId);
                    mysqli_stmt_execute($updateStmt);
                }

                $userSubscriptionLevel = 'free';
            }

            // Set final subscription level
            $finalSubscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';

            // Stripe Customer ID'yi ekle (null olabilir)
            $user["stripeCustomerId"] = $user["stripe_customer_id"];
            unset($user["stripe_customer_id"]); // Orijinal alanı kaldır

            // Add validated subscription level to response
            $user["subscriptionLevel"] = $finalSubscriptionLevel;
            unset($user["subscription_level"]); // Remove original field
            unset($user["stripe_status"]); // Remove internal field
            unset($user["plan_name"]); // Remove internal field
            // Stripe Subscription ID'yi bul (varsa) - already retrieved in main query
            $user["stripeSubscriptionId"] = null;
            if ($user["stripeCustomerId"] && !empty($stripeStatus)) {
                // We already have the subscription data from the main query
                // Get the stripe_subscription_id from stripe_user_subscriptions table
                $subscriptionQuery = "SELECT stripe_subscription_id FROM stripe_user_subscriptions
                                     WHERE user_id = ? AND status IN ('active', 'trialing')
                                     ORDER BY created_at DESC LIMIT 1";
                $subscriptionStmt = mysqli_prepare($link, $subscriptionQuery);
                if ($subscriptionStmt) {
                    mysqli_stmt_bind_param($subscriptionStmt, "i", $userId);
                    mysqli_stmt_execute($subscriptionStmt);
                    $subscriptionResult = mysqli_stmt_get_result($subscriptionStmt);
                    $subscription = mysqli_fetch_assoc($subscriptionResult);
                    if ($subscription && isset($subscription["stripe_subscription_id"])) {
                        $user["stripeSubscriptionId"] = $subscription["stripe_subscription_id"];
                    }
                }
            }
            // Profil fotoğrafı URL'sini ekle
            // Profil image kullanilmadigi icin kaldirdik
            // $user["profileImage"] = $user["profile_image"] ? str_replace('\\', '/', $user["profile_image"]) : null;
            // unset($user["profile_image"]); // Orijinal alanı kaldır

            // Eksik alanlar için statik veriler ekleniyor
            // $user["bio"] = "Crypto enthusiast and developer.";
            $user["membershipTier"] = $finalSubscriptionLevel; // Use validated subscription level
            // $user["createdAt"] = "2024-01-01 12:00:00"; // DB'den çekilecek
            // $user["lastLogin"] = "2024-03-14 08:30:00"; // DB'den çekilecek
            // $user["notificationPreferences"] = [
            //     "email" => true,
            //     "push" => false,
            //     "telegram" => true
            // ];
            $resp["user"] = $user;
            $response = new SuccessResult($resp);
            $response->send();
        } else {
            $response = new ErrorResult("User not found.");
            $response->send(404);
        }
    } else {
        $response = new ErrorResult("Database error.");
        $response->send(500);
    }
}
function search_assets($searchText)
{
    global $link;
    // Include StripeLogger
    require_once 'stripe/StripeLogger.php';
    // İzin kontrolü - Kullanıcının arama yapıp yapamayacağını kontrol et
    $userId = getUserIdFromJWT();
    // Debug için kullanıcı izinlerini loglayalım
    StripeLogger::log(StripeLogLevel::INFO, "Search_assets called with text: " . $searchText, [
        'user_id' => $userId
    ]);
    // Kullanıcının abonelik seviyesini güvenli şekilde al (Stripe subscription status ile doğrula)
    $subscriptionLevel = 'free';
    if ($userId) {
        $query = "SELECT u.subscription_level, s.status as stripe_status
                  FROM users u
                  LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                      AND s.status IN ('active', 'trialing')
                  WHERE u.id = ?
                  ORDER BY s.created_at DESC
                  LIMIT 1";
        $stmt = mysqli_prepare($link, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "i", $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);
            $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
            $stripeStatus = $user['stripe_status'];

            // Security check: Only allow paid features if Stripe subscription is active
            $subscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';

            StripeLogger::log(StripeLogLevel::INFO, "get_coin_detail_by_id - User subscription validation", [
                'user_id' => $userId,
                'user_subscription_level' => $userSubscriptionLevel,
                'stripe_status' => $stripeStatus,
                'final_subscription_level' => $subscriptionLevel
            ]);
        } else {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "Failed to prepare statement for getting subscription level", [
                'user_id' => $userId,
                'error' => $error
            ]);
        }
    } else {
        StripeLogger::log(StripeLogLevel::WARNING, "No user ID found in JWT", [
            'search_text' => $searchText
        ]);
    }
    // Get user permissions for detailed logging
    $userPermissions = getUserPermissions($userId);
    StripeLogger::log(StripeLogLevel::INFO, "User permissions retrieved", [
        'user_id' => $userId,
        'permissions' => $userPermissions ? json_encode($userPermissions) : 'null',
        'permissions_type' => gettype($userPermissions)
    ]);
    // Temel arama izni kontrolü - Sadece izin kontrolü yapılıyor
    $hasSearchPermission = hasUserPermission($userId, 'can_perform_basic_search');
    StripeLogger::log(StripeLogLevel::INFO, "Search permission check result: " . ($hasSearchPermission ? "true" : "false"), [
        'user_id' => $userId,
        'permission' => 'can_perform_basic_search',
        'has_permission' => $hasSearchPermission,
        'subscription_level' => $subscriptionLevel
    ]);
    if (!$hasSearchPermission) {
        // Kullanıcının arama izni yok
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $errorMessage = $clientMessages[$lang]['search_subscription_required'];
        StripeLogger::log(StripeLogLevel::INFO, "Search permission denied", [
            'user_id' => $userId,
            'subscription_level' => $subscriptionLevel,
            'error_message' => $errorMessage
        ]);
        $response = new ErrorResult($errorMessage);
        $response->send(403);
        return;
    }
    // Boş sonuç dizisi oluştur
    $results = [
        'coins' => [],
        'idos' => []
    ];
    // Boş arama metni kontrolü
    if (empty($searchText)) {
        StripeLogger::log(StripeLogLevel::INFO, "Empty search text, returning empty results", [
            'user_id' => $userId
        ]);
        (new SuccessResult($results))->send();
        return;
    }
    // SQL injection koruması
    $escapedText = mysqli_real_escape_string($link, $searchText);
    // Sabit sonuç limiti - Karakter sayısından bağımsız olarak aynı limit kullanılıyor
    $resultLimit = 10;
    StripeLogger::log(StripeLogLevel::INFO, "Search result limit set to: " . $resultLimit, [
        'user_id' => $userId,
        'search_text' => $searchText
    ]);
    // Coin araması
    $coinQuery = "
        SELECT id, name, symbol, image
        FROM client_coin_list
        WHERE isactive = 1
          AND (name LIKE '%$escapedText%' OR symbol LIKE '%$escapedText%')
        ORDER BY marketcap DESC
        LIMIT $resultLimit";
    StripeLogger::log(StripeLogLevel::INFO, "Executing coin search query", [
        'user_id' => $userId,
        'query' => $coinQuery
    ]);
    $coinRs = mysqli_query($link, $coinQuery);
    if ($coinRs) {
        while ($row = mysqli_fetch_assoc($coinRs)) {
            $results['coins'][] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'symbol' => $row['symbol'],
                'image' => $row['image']
            ];
        }
        StripeLogger::log(StripeLogLevel::INFO, "Found " . count($results['coins']) . " coins matching the search", [
            'user_id' => $userId,
            'search_text' => $searchText
        ]);
    } else {
        $error = mysqli_error($link);
        StripeLogger::log(StripeLogLevel::ERROR, "Coin search query failed", [
            'user_id' => $userId,
            'error' => $error,
            'query' => $coinQuery
        ]);
    }
    // IDO araması - Tüm kullanıcılar için IDO sonuçlarını göster
    $idoQuery = "
        SELECT id, cr_name, symbol, image
        FROM client_ico_coin_list
        WHERE cr_name LIKE '%$escapedText%' OR symbol LIKE '%$escapedText%'
        ORDER BY initial_marketcap DESC
        LIMIT $resultLimit";
    StripeLogger::log(StripeLogLevel::INFO, "Executing IDO search query", [
        'user_id' => $userId,
        'query' => $idoQuery
    ]);
    $idoRs = mysqli_query($link, $idoQuery);
    if ($idoRs) {
        while ($row = mysqli_fetch_assoc($idoRs)) {
            $results['idos'][] = [
                'id' => $row['id'],
                'name' => $row['cr_name'],
                'symbol' => $row['symbol'],
                'image' => $row['image']
            ];
        }
        StripeLogger::log(StripeLogLevel::INFO, "Found " . count($results['idos']) . " IDOs matching the search", [
            'user_id' => $userId,
            'search_text' => $searchText
        ]);
    } else {
        $error = mysqli_error($link);
        StripeLogger::log(StripeLogLevel::ERROR, "IDO search query failed", [
            'user_id' => $userId,
            'error' => $error,
            'query' => $idoQuery
        ]);
    }
    // Her durumda başarılı bir yanıt döndür
    StripeLogger::log(StripeLogLevel::INFO, "Sending search results", [
        'user_id' => $userId,
        'search_text' => $searchText,
        'coins_count' => count($results['coins']),
        'idos_count' => count($results['idos'])
    ]);
    (new SuccessResult($results))->send();
}
function search_coins($searchText)
{
    global $link;
    require_once 'stripe/StripeLogger.php';

    $userId = getUserIdFromJWT();
    StripeLogger::log(StripeLogLevel::INFO, "Search_coins called with text: " . $searchText, [
        'user_id' => $userId
    ]);

    $subscriptionLevel = 'free';
    if ($userId) {
        $query = "SELECT u.subscription_level, s.status as stripe_status
                  FROM users u
                  LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                      AND s.status IN ('active', 'trialing')
                  WHERE u.id = ?
                  ORDER BY s.created_at DESC
                  LIMIT 1";
        $stmt = mysqli_prepare($link, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "i", $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);
            $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
            $stripeStatus = $user['stripe_status'];

            // Security check: Only allow paid features if Stripe subscription is active
            $subscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';

            StripeLogger::log(StripeLogLevel::INFO, "get_adv_coin_detail_by_id - User subscription validation", [
                'user_id' => $userId,
                'user_subscription_level' => $userSubscriptionLevel,
                'stripe_status' => $stripeStatus,
                'final_subscription_level' => $subscriptionLevel
            ]);
        } else {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "Failed to prepare statement for getting subscription level", [
                'user_id' => $userId,
                'error' => $error
            ]);
        }
    } else {
        StripeLogger::log(StripeLogLevel::WARNING, "No user ID found in JWT", [
            'search_text' => $searchText
        ]);
    }
    $userPermissions = getUserPermissions($userId);
    StripeLogger::log(StripeLogLevel::INFO, "User permissions retrieved", [
        'user_id' => $userId,
        'permissions' => $userPermissions ? json_encode($userPermissions) : 'null',
        'permissions_type' => gettype($userPermissions)
    ]);
    $hasSearchPermission = hasUserPermission($userId, 'can_perform_coins_search');
    StripeLogger::log(StripeLogLevel::INFO, "Search permission check result: " . ($hasSearchPermission ? "true" : "false"), [
        'user_id' => $userId,
        'permission' => 'can_perform_coins_search',
        'has_permission' => $hasSearchPermission,
        'subscription_level' => $subscriptionLevel
    ]);

    if (!$hasSearchPermission) {
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
        $errorMessage = $clientMessages[$lang]['search_subscription_required'];
        StripeLogger::log(StripeLogLevel::INFO, "Search permission denied", [
            'user_id' => $userId,
            'subscription_level' => $subscriptionLevel,
            'error_message' => $errorMessage
        ]);
        $response = new ErrorResult($errorMessage);
        $response->send(403);
        return;
    }

    $results = [
        'coins' => []
    ];

    if (empty($searchText)) {
        StripeLogger::log(StripeLogLevel::INFO, "Empty search text, returning empty results", [
            'user_id' => $userId
        ]);
        (new SuccessResult($results))->send();
        return;
    }

    $escapedText = mysqli_real_escape_string($link, $searchText);
    $resultLimit = 10;
    StripeLogger::log(StripeLogLevel::INFO, "Search result limit set to: " . $resultLimit, [
        'user_id' => $userId,
        'search_text' => $searchText
    ]);

    $coinQuery = "
        SELECT id, name, symbol, image
        FROM client_coin_list
        WHERE isactive = 1
          AND (name LIKE '%$escapedText%' OR symbol LIKE '%$escapedText%')
        ORDER BY marketcap DESC
        LIMIT $resultLimit";
    StripeLogger::log(StripeLogLevel::INFO, "Executing coin search query", [
        'user_id' => $userId,
        'query' => $coinQuery
    ]);
    $coinRs = mysqli_query($link, $coinQuery);
    if ($coinRs) {
        while ($row = mysqli_fetch_assoc($coinRs)) {
            $results['coins'][] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'symbol' => $row['symbol'],
                'image' => $row['image']
            ];
        }
        StripeLogger::log(StripeLogLevel::INFO, "Found " . count($results['coins']) . " coins matching the search", [
            'user_id' => $userId,
            'search_text' => $searchText
        ]);
    } else {
        $error = mysqli_error($link);
        StripeLogger::log(StripeLogLevel::ERROR, "Coin search query failed", [
            'user_id' => $userId,
            'error' => $error,
            'query' => $coinQuery
        ]);
    }

    StripeLogger::log(StripeLogLevel::INFO, "Sending search results", [
        'user_id' => $userId,
        'search_text' => $searchText,
        'coins_count' => count($results['coins'])
    ]);
    (new SuccessResult($results))->send();
}

function get_coins_list($marketcap_min, $marketcap_max, $score_min, $score_max, $categories, $chains)
{
    global $link;
    $our_data = array();
    $filters = ["isactive = 1"];
    if ($marketcap_min !== null) {
        $filters[] = "marketcap >= $marketcap_min";
    }
    if ($marketcap_max !== null) {
        $filters[] = "marketcap <= $marketcap_max";
    }
    if ($score_min !== null) {
        $filters[] = "total_score >= $score_min";
    }
    if ($score_max !== null) {
        $filters[] = "total_score <= $score_max";
    }
    if (!empty($categories)) {
        $categories_list = "'" . implode("','", array_map(function ($category) use ($link) {
            return mysqli_real_escape_string($link, $category);
        }, $categories)) . "'";
        $filters[] = "geckoid IN (
            SELECT geckoid FROM coindata_categories WHERE category_name IN ($categories_list)
        )";
    }
    if (!empty($chains)) {
        $chains_list = "'" . implode("','", array_map(function ($chain) use ($link) {
            return mysqli_real_escape_string($link, $chain);
        }, $chains)) . "'";
        $filters[] = "geckoid IN (
            SELECT geckoid FROM coindata_categories WHERE category_name IN ($chains_list)
        )";
    }
    $filter_string = implode(" AND ", $filters);
    //400ms civarı cevap geliyr
    $query = "SELECT id, geckoid, name, image, symbol, total_score, marketcap_rank,
    tokenomicsScore, securityScore, socialScore, marketScore, fundamentalsScore
        FROM client_coin_list
        WHERE $filter_string
        ORDER BY marketcap DESC";
    $rs = mysqli_query($link, $query);
    if ($rs) {
        $pos = 1;
        while ($obj2 = mysqli_fetch_assoc($rs)) {
            $obj2['pos'] = $pos;
            $pos++;
            // $gecko_id değişkeni kullanılmıyor, kaldırıldı
            $obj2['total_score'] = floatval($obj2['total_score']);
            $obj2['tokenomicsScore'] = floatval($obj2['tokenomicsScore']);
            $obj2['securityScore'] = floatval($obj2['securityScore']);
            $obj2['socialScore'] = floatval($obj2['socialScore']);
            $obj2['marketScore'] = floatval($obj2['marketScore']);
            $obj2['fundamentalsScore'] = floatval($obj2['fundamentalsScore']);
            //200ms - 300ms civarı cevap geliyor.
            /*
    $query = "
        SELECT id, geckoid, name, image, symbol, total_score, marketcap_rank
        FROM coindata
        WHERE $filter_string
        ORDER BY marketcap DESC";
    $rs = mysqli_query($link, $query);
    if ($rs) {
        $pos = 1;
        while ($obj2 = mysqli_fetch_assoc($rs)) {
            $obj2['pos'] = $pos;
            $pos++;
            $gecko_id = $obj2['geckoid'];
            $obj2['total_score'] = floatval($obj2['total_score']);
            $obj2['tokenomicsScore'] = 0;
            $obj2['securityScore'] = 0;
            $obj2['socialScore'] = 0;
            $obj2['marketScore'] = 0;
            $obj2['fundamentalsScore'] = 0;
            if(isset($coin_scores[$gecko_id][1]))
            $obj2['tokenomicsScore'] = floatval($coin_scores[$gecko_id][1]);
            if(isset($coin_scores[$gecko_id][3]))
            $obj2['securityScore'] = floatval($coin_scores[$gecko_id][3]);
            if(isset($coin_scores[$gecko_id][4]))
            $obj2['socialScore'] = floatval($coin_scores[$gecko_id][4]);
            if(isset($coin_scores[$gecko_id][5]))
            $obj2['marketScore'] = floatval($coin_scores[$gecko_id][5]);
            if(isset($coin_scores[$gecko_id][6]))
            $obj2['fundamentalsScore'] = floatval($coin_scores[$gecko_id][6]);
            */
            // $obj2['tokenomicsScore'] = $obj2['tokenomicsScore'] ?? 0;
            // $obj2['securityScore'] = $obj2['securityScore'] ?? 0;
            // $obj2['socialScore'] = $obj2['socialScore'] ?? 0;
            // $obj2['marketScore'] = $obj2['marketScore'] ?? 0;
            // $obj2['fundamentalsScore'] = $obj2['fundamentalsScore'] ?? 0;
            $obj2['change'] = rand(-99, 100);
            $our_data[] = $obj2;
        }
        $response = new SuccessResult($our_data);
        $response->send();
    } else {
        $response = new ErrorResult('Veritabanı sorgusu başarısız.');
        $response->send(500);
    }
}
function get_coins_search_list()
{
    global $link;
    $our_data = array();
    $query = "SELECT id, name, geckoid FROM coindata_all";
    $rs = mysqli_query($link, $query);
    if ($rs) {
        while ($obj2 = mysqli_fetch_assoc($rs)) {
            $our_data[] = $obj2;
        }
        $response = new SuccessResult($our_data);
        $response->send();
    } else {
        $response = new ErrorResult('Database query failed.');
        $response->send(500);
    }
}
function get_categories()
{
    global $link;
    $query = "SELECT cr_id as id, cr_name as name FROM cr_tags";
    $rs = mysqli_query($link, $query);
    if ($rs) {
        $categories = array();
        while ($category = mysqli_fetch_assoc($rs)) {
            $categories[] = $category;
        }
        $response = new SuccessResult($categories);
        $response->send();
    } else {
        $response = new ErrorResult('Kategori verisi çekilemedi.');
        $response->send(500);
    }
}
function get_dashboard_highlights()
{
    global $link;
    $categories = ['topGainers', 'newListings', 'upcomingIDOs'];
    $results = [];
    foreach ($categories as $key) {
        $coins = [];
        if ($key === 'topGainers') {
            $query = "SELECT id, name, image, total_score as totalScore,
                     sevenDayChange as priceChangePercentage24h
              FROM client_coin_list
              WHERE isactive = 1 AND sevenDayChange > 0.1
              ORDER BY sevenDayChange DESC, name ASC
              LIMIT 3";
            $rs = mysqli_query($link, $query);
            if ($rs) {
                while ($row = mysqli_fetch_assoc($rs)) {
                    $row['gainPercentage'] = $row['priceChangePercentage24h'];
                    $row['symbol'] = isset($row['symbol']) ? $row['symbol'] : strtoupper(substr($row['name'], 0, 3));
                    $row['price'] = isset($row['price']) ? $row['price'] : round(rand(1, 1000) + rand() / getrandmax(), 2);
                    $coins[] = $row;
                }
            }
        } elseif ($key === 'newListings') {
            // 6 aylık filtre ile get_coins_list_v2 ile tutarlı hale getiriliyor
            $query = "SELECT id, name, image, symbol, total_score as totalScore,
            coin_age
              FROM client_coin_list
              WHERE isactive = 1 AND coin_age >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 6 MONTH))
              AND coin_age <= UNIX_TIMESTAMP(NOW())
               ORDER BY coin_age DESC, name ASC
                      LIMIT 3";
            $rs = mysqli_query($link, $query);
            if ($rs) {
                while ($row = mysqli_fetch_assoc($rs)) {
                    $coins[] = $row;
                }
            }
        } elseif ($key === 'upcomingIDOs') {
            $query = "SELECT id, symbol, cr_name, image, total_score FROM client_ico_coin_list ORDER BY total_score DESC LIMIT 3";
            $rs = mysqli_query($link, $query);
            if ($rs) {
                while ($row = mysqli_fetch_assoc($rs)) {
                    $coinData = [
                        'id' => $row['id'],
                        'name' => $row['cr_name'],
                        'symbol' => $row['symbol'],
                        'image' => $row['image'],
                        'totalScore' => $row['total_score'],
                        'launchDate' => 'WIP',
                        'launchPlatform' => 'WIP'
                    ];
                    $coins[] = $coinData;
                }
            }
        }
        $results[$key] = $coins;
    }
    (new SuccessResult($results))->send();
}
function get_upcoming_idos()
{
    global $link;
    $results = [];
    $query = "SELECT id, symbol, cr_name, image FROM cr_publicsales ORDER BY id";
    $rs = mysqli_query($link, $query);
    if ($rs) {
        while ($row = mysqli_fetch_assoc($rs)) {
            $results[] = [
                'id' => (string)$row['id'],
                'name' => $row['cr_name'],
                'symbol' => $row['symbol'],
                'logo' => $row['image'],
                'launchDate' => date('Y-m-d', strtotime('+' . rand(1, 60) . ' days')),
                'launchType' => ['ICO', 'IDO', 'EVET'][rand(0, 2)],
                'imcScore' => [
                    'score' => $score = rand(10, 100),
                    'status' => getScoreStatus($score),
                    'valueText' => rand(1, 100) . ' milyon dolar'
                ],
                'financingScore' => [
                    'score' => $score = rand(10, 100),
                    'status' => getScoreStatus($score),
                    'valueText' => rand(1, 50) . ' milyon dolar'
                ],
                'launchpadScore' => [
                    'score' => $score = rand(10, 100),
                    'status' => getScoreStatus($score),
                    'valueText' => 'Onaylandı ' . rand(1, 3)
                ],
                'investorScore' => [
                    'score' => $score = rand(10, 100),
                    'status' => getScoreStatus($score),
                    'valueText' => rand(1, 10) . ' Doğrulandı'
                ],
                'totalAiScore' => rand(10, 100),
                'isFavorite' => false,
                'detailsUrl' => '/coin/' . strtolower($row['symbol'])
            ];
        }
    }
    (new SuccessResult($results))->send();
}
/**
 * Returns upcoming IDOs in a format matching the UpcomingCoin interface
 *
 * @return void JSON response is directly echoed
 */
function get_upcoming_idos_v2()
{
    global $link;

    // Include StripeLogger for logging
    require_once 'stripe/StripeLogger.php';

    // Get user ID from JWT token (optional - no authentication required)
    $userId = getUserIdFromJWT();
    $subscriptionLevel = 'free'; // Default to free

    // If user is authenticated, get their validated subscription level
    if ($userId) {
        $query = "SELECT u.subscription_level, s.status as stripe_status
                  FROM users u
                  LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                      AND s.status IN ('active', 'trialing')
                  WHERE u.id = ?
                  ORDER BY s.created_at DESC
                  LIMIT 1";
        $stmt = mysqli_prepare($link, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "i", $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);
            $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
            $stripeStatus = $user['stripe_status'];

            // Security check: Only allow paid features if Stripe subscription is active
            $subscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';

            StripeLogger::log(StripeLogLevel::INFO, "get_upcoming_idos_v2 called by authenticated user", [
                'user_id' => $userId,
                'user_subscription_level' => $userSubscriptionLevel,
                'stripe_status' => $stripeStatus,
                'final_subscription_level' => $subscriptionLevel
            ]);
        }
    } else {
        StripeLogger::log(StripeLogLevel::INFO, "get_upcoming_idos_v2 called by unauthenticated user (free access)");
    }

    // For free users, we need to get total count first
    $isFreeUser = ($subscriptionLevel === 'free');
    $totalCount = 0;

    if ($isFreeUser) {
        // Get total count for free users
        $countQuery = "SELECT COUNT(*) as total FROM client_ico_coin_list";
        $countRs = mysqli_query($link, $countQuery);
        if ($countRs) {
            $countRow = mysqli_fetch_assoc($countRs);
            $totalCount = (int)$countRow['total'];

            StripeLogger::log(StripeLogLevel::INFO, "Free user - total IDOs available", [
                'user_id' => $userId,
                'total_count' => $totalCount
            ]);
        }
    }

    // Main query with limit for free users
    $query = "SELECT
        id,
        cr_name,
        symbol,
        image,
        total_score,
        imc_score,
        funding_score,
        launchpad_score,
        investor_score,
        social_score,
        crowdsale_startdate,
        crowdsale_enddate,
        ido_type
    FROM client_ico_coin_list
    ORDER BY total_score DESC";

    // Add limit for free users
    if ($isFreeUser) {
        $query .= " LIMIT 10";
    }

    $rs = mysqli_query($link, $query);
    if (!$rs) {
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        (new ErrorResult($clientMessages[$lang]['database_query_failed_simple']))->send(500);
        return;
    }

    $results = [];

    // Add actual IDO data
    while ($row = mysqli_fetch_assoc($rs)) {
        // Use actual launch date from database (Unix timestamp format)
        $launchDate = null;
        if (!empty($row['crowdsale_startdate']) && $row['crowdsale_startdate'] > 0) {
            $launchDate = date('Y-m-d', $row['crowdsale_startdate']);
        } elseif (!empty($row['crowdsale_enddate']) && $row['crowdsale_enddate'] > 0) {
            $launchDate = date('Y-m-d', $row['crowdsale_enddate']);
        }

        // Use actual IDO type from database or default to TBA
        $launchType = !empty($row['ido_type']) ? $row['ido_type'] : "TBA";
        // Format scores with their status
        $imcScore = [
            'score' => (int)$row['imc_score'],
            'status' => getScoreStatus((int)$row['imc_score'])
        ];
        $financingScore = [
            'score' => (int)$row['funding_score'],
            'status' => getScoreStatus((int)$row['funding_score'])
        ];
        $launchpadScore = [
            'score' => (int)$row['launchpad_score'],
            'status' => getScoreStatus((int)$row['launchpad_score'])
        ];
        $investorScore = [
            'score' => (int)$row['investor_score'],
            'status' => getScoreStatus((int)$row['investor_score'])
        ];
        $socialScore = [
            'score' => (int)$row['social_score'],
            'status' => getScoreStatus((int)$row['social_score'])
        ];
        $totalAiScore = [
            'score' => (int)$row['total_score'],
            'status' => getScoreStatus((int)$row['total_score'])
        ];
        $results[] = [
            'id' => (string)$row['id'],
            'name' => $row['cr_name'],
            'symbol' => $row['symbol'],
            'image' => $row['image'],
            'launchDate' => $launchDate,
            'launchType' => $launchType,
            'imcScore' => $imcScore,
            'financingScore' => $financingScore,
            'launchpadScore' => $launchpadScore,
            'investorScore' => $investorScore,
            'totalAiScore' => $totalAiScore,
            'socialScore' => $socialScore
        ];
    }

    // Prepare meta information for free users
    $meta = null;
    if ($isFreeUser && $totalCount > 10) {
        $remainingCount = $totalCount - 10;
        $meta = [
            'isDemo' => true,
            'count' => $remainingCount
        ];

        StripeLogger::log(StripeLogLevel::INFO, "Added meta information for free user", [
            'user_id' => $userId,
            'remaining_count' => $remainingCount,
            'total_count' => $totalCount
        ]);
    }

    StripeLogger::log(StripeLogLevel::INFO, "get_upcoming_idos_v2 response prepared", [
        'user_id' => $userId,
        'subscription_level' => $subscriptionLevel,
        'is_free_user' => $isFreeUser,
        'idos_returned' => count($results),
        'has_meta' => $meta !== null
    ]);

    (new SuccessResult($results, $meta))->send();
}
function get_chains()
{
    global $link;
    $query = "SELECT name FROM gecko_categories WHERE name LIKE '%ecosystem%' AND isshown = 0 ORDER BY name ASC";
    $rs = mysqli_query($link, $query);
    if ($rs) {
        $categories = array();
        while ($category = mysqli_fetch_assoc($rs)) {
            $categories[] = $category;
        }
        $response = new SuccessResult($categories);
        $response->send();
    } else {
        $response = new ErrorResult('Kategori verisi çekilemedi.');
        $response->send(500);
    }
}
function get_metric_texts_by_id($id, $coin_id = null)
{
    global $selectedLanguage;
    global $link;
    $id = intval($id);
    $languageColumn = "description_" . strtoupper($selectedLanguage);
    $whatAreWeScoringColumn = "what_are_we_scoring_" . strtoupper($selectedLanguage);
    $whyIsThisImportantColumn = "why_is_this_important_" . strtoupper($selectedLanguage);
    $languageColumn = mysqli_real_escape_string($link, $languageColumn);
    $whatAreWeScoringColumn = mysqli_real_escape_string($link, $whatAreWeScoringColumn);
    $whyIsThisImportantColumn = mysqli_real_escape_string($link, $whyIsThisImportantColumn);
    $query = "SELECT
        `$languageColumn` AS conclusion_text,
        `$whatAreWeScoringColumn` AS what_are_we_scoring,
        `$whyIsThisImportantColumn` AS why_is_this_important
    FROM `metric_translations`
    WHERE metric_id = $id";
    $result = mysqli_query($link, $query);
    if ($result && mysqli_num_rows($result) > 0) {
        $metricGroup = mysqli_fetch_assoc($result);

        // Eğer coin_id verilmişse, metric_specials tablosundan ek veriler çek
        if ($coin_id !== null) {
            $coin_id = mysqli_real_escape_string($link, $coin_id);

            // coin_id (geckoid) değerini coindata tablosundan cr_id'ye çevir
            $crIdQuery = "SELECT cr_id FROM coindata WHERE id = '$coin_id'";
            $crIdResult = mysqli_query($link, $crIdQuery);
            $cr_id = null;

            if ($crIdResult && mysqli_num_rows($crIdResult) > 0) {
                $crIdRow = mysqli_fetch_assoc($crIdResult);
                $cr_id = $crIdRow['cr_id'];
            }

            // cr_id bulunamadıysa işlemi sonlandır
            if ($cr_id === null) {
                $response = new SuccessResult($metricGroup);
                $response->send();
                return;
            }
            error_log("cr_id: " . $cr_id);

            // Metric ID'ye göre hangi key'lerin çekileceğini belirle
            $requiredKeys = getRequiredKeysForMetric($id);

            if (!empty($requiredKeys)) {
                $keysList = "'" . implode("','", array_map(function ($key) use ($link) {
                    return mysqli_real_escape_string($link, $key);
                }, $requiredKeys)) . "'";

                $specialsQuery = "SELECT `metric_key` as 'key', `metric_value` as 'value'
                                FROM metric_specials
                                WHERE metric_id = $id
                                AND coin_id = '$cr_id'
                                AND `metric_key` IN ($keysList)";

                $specialsResult = mysqli_query($link, $specialsQuery);
                $specialsData = [];

                if ($specialsResult && mysqli_num_rows($specialsResult) > 0) {
                    while ($row = mysqli_fetch_assoc($specialsResult)) {
                        // Special processing metrics that require complex data transformation
                        if ($id == 36 && $row['key'] == 'token_use_case') {
                            $specialsData[$row['key']] = processUseCaseData($row['value']);
                        } else if ($id == 38 && $row['key'] == 'def_or_inf') {
                            $specialsData[$row['key']] = processTokenomicsData($row['value']);
                        } else if ($id == 39 && $row['key'] == 'cross_chain_rating') {
                            // For now, use simple value until special processing is needed
                            $specialsData[$row['key']] = $row['value'];
                        } else if ($id == 40 && $row['key'] == 'token_redist_score') {
                            $specialsData[$row['key']] = processTokenRedistributionData($row['value']);
                        } else if ($id == 41 && $row['key'] == 'token_buyback') {
                            $specialsData[$row['key']] = processTokenBuybackData($row['value']);
                        } else if ($id == 42 && $row['key'] == 'revenue_sharing') {
                            $specialsData[$row['key']] = processRevenueSharingData($row['value']);
                        } else if ($id == 50 && $row['key'] == 'team_anonymity') {
                            $specialsData[$row['key']] = processTeamAnonymityData($row['value']);
                        } else if ($id == 52 && $row['key'] == 'dao_governance') {
                            $specialsData[$row['key']] = processDaoGovernanceData($row['value']);
                        }
                        // All other metrics use simple value retrieval
                        else {
                            $specialsData[$row['key']] = $row['value'];
                        }
                    }
                }

                // Hesaplama verilerini ana objeye ekle (metric ID'ye göre gruplandırılmış)
                if (!isset($metricGroup['calculation_data'])) {
                    $metricGroup['calculation_data'] = [];
                }
                $metricGroup['calculation_data']['m' . $id] = $specialsData;
            }
        }

        $response = new SuccessResult($metricGroup);
        $response->send();
    } else {
        $response = new ErrorResult('Metric group not found.');
        $response->send(404);
    }
}

/**
 * Metric ID'ye göre gerekli key'leri döndürür
 * Her metric için hangi hesaplama verilerinin çekileceğini belirler
 */
function getRequiredKeysForMetric($metricId)
{
    $keyMappings = [
        // Simple value metrics (no special processing)
        1 => ['mcap_fdv_ratio'],
        2 => ['max_supply'],
        3 => ['code_security'],
        4 => ['community_trust'],
        5 => ['total_volume'],
        6 => ['best_cex_rank'],
        7 => ['best_dex_rank'],
        8 => ['cex_count'],
        9 => ['dex_count'],
        11 => ['vesting_schedule'],
        12 => ['emission_score_1y'],
        14 => ['risk_reward_rating'],
        17 => ['fundamental_health'],
        18 => ['governance_strength'],
        19 => ['market_stability'],
        20 => ['operational_resilience'],
        23 => ['risk_reward_rating'],
        24 => ['alt_rank'],
        27 => ['sentiment'],
        28 => ['gecko_portfolio_count'],
        29 => ['social_volume_24h'],
        30 => ['social_dominance'],

        // Special processing metrics
        36 => ['token_use_case'],
        38 => ['def_or_inf'],
        39 => ['cross_chain_rating'],
        40 => ['token_redist_score'],
        41 => ['token_buyback'],
        42 => ['revenue_sharing'],
        50 => ['team_anonymity'],
        52 => ['dao_governance'],

        // Yeni metric ID'ler için key'ler buraya eklenebilir
    ];

    return isset($keyMappings[$metricId]) ? $keyMappings[$metricId] : [];
}





// Email-related methods (report_error_v2, request_feature, contact_support_request)
// have been moved to email_services.php
/**
 * Belirtilen coin ID'leri için detaylı bilgileri döndürür
 *
 * @param int $userId Kullanıcı ID'si (JWT'den alınır)
 * @param array $coin_ids Detayları alınacak coin ID'leri (geckoid)
 * @return void JSON yanıtı doğrudan echo edilir
 */
function compare_coins($userId, $coin_ids)
{
    global $link;
    global $selectedLanguage;
    global $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    // Include StripeLogger for logging
    require_once 'stripe/StripeLogger.php';

    // Get user's validated subscription level
    $subscriptionLevel = 'free'; // Default to free
    $query = "SELECT u.subscription_level, s.status as stripe_status
              FROM users u
              LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                  AND s.status IN ('active', 'trialing')
              WHERE u.id = ?
              ORDER BY s.created_at DESC
              LIMIT 1";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
        $stripeStatus = $user['stripe_status'];

        // Security check: Only allow paid features if Stripe subscription is active
        $subscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';
    }

    StripeLogger::log(StripeLogLevel::INFO, "compare_coins called", [
        'user_id' => $userId,
        'subscription_level' => $subscriptionLevel,
        'coin_count' => count($coin_ids)
    ]);

    // Check usage limit for free users
    if ($subscriptionLevel === 'free') {
        // Check usage in last 30 days
        $usageQuery = "SELECT SUM(usage_count) as total_usage
                       FROM user_feature_usage
                       WHERE user_id = ?
                         AND feature_name = 'compare_coins'
                         AND usage_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";

        $usageStmt = mysqli_prepare($link, $usageQuery);
        if ($usageStmt) {
            mysqli_stmt_bind_param($usageStmt, "i", $userId);
            mysqli_stmt_execute($usageStmt);
            $usageResult = mysqli_stmt_get_result($usageStmt);
            $usageRow = mysqli_fetch_assoc($usageResult);
            $totalUsage = (int)($usageRow['total_usage'] ?? 0);

            StripeLogger::log(StripeLogLevel::DEBUG, "Free user compare_coins usage check", [
                'user_id' => $userId,
                'total_usage_30_days' => $totalUsage,
                'limit' => 8
            ]);

            if ($totalUsage >= 8) {
                StripeLogger::log(StripeLogLevel::INFO, "Free user exceeded compare_coins limit", [
                    'user_id' => $userId,
                    'total_usage_30_days' => $totalUsage,
                    'limit' => 8
                ]);

                global $selectedLanguage, $clientMessages;
                $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

                $response = new ErrorResult($clientMessages[$lang]['monthly_limit_reached']);
                $response->send(403);
                return;
            }

            // Record this usage
            $recordUsageQuery = "INSERT INTO user_feature_usage (user_id, feature_name, usage_date, usage_count)
                                VALUES (?, 'compare_coins', CURDATE(), 1)
                                ON DUPLICATE KEY UPDATE usage_count = usage_count + 1";

            $recordStmt = mysqli_prepare($link, $recordUsageQuery);
            if ($recordStmt) {
                mysqli_stmt_bind_param($recordStmt, "i", $userId);
                mysqli_stmt_execute($recordStmt);

                StripeLogger::log(StripeLogLevel::INFO, "Recorded compare_coins usage for free user", [
                    'user_id' => $userId,
                    'new_usage_count' => $totalUsage + 1
                ]);
            }
        }
    } else {
        StripeLogger::log(StripeLogLevel::DEBUG, "Paid user - no usage limit for compare_coins", [
            'user_id' => $userId,
            'subscription_level' => $subscriptionLevel
        ]);
    }
    if (count($coin_ids) > 5) {
        http_response_code(400);
        echo json_encode(['error' => $clientMessages[$lang]['max_coins_limit']]);
        return;
    }
    $clean_ids = array_map(function ($id) {
        global $link;
        return mysqli_real_escape_string($link, $id);
    }, $coin_ids);
    $clean_ids = array_filter($clean_ids);
    if (empty($clean_ids)) {
        http_response_code(400);
        echo json_encode(['error' => $clientMessages[$lang]['no_valid_coin_id']]);
        return;
    }
    $id_list = "'" . implode("','", $clean_ids) . "'";
    $query = "
    SELECT
        c.id,
        c.name,
        c.symbol,
        c.image,
        c.marketcap,
        c.marketcap_rank,
        c.fdv,
        c.total_volume,
        c.total_supply,
        c.max_supply,
        c.circulating_supply,
        c.geckoid,
        c2.price_change_1d,
        c2.price_change_7d,
        c2.price_change_30d,
        c2.price_change_90d,
        c2.price_change_1y
    FROM coindata c
    LEFT JOIN coindata2 c2 ON c.geckoid = c2.geckoid
    WHERE c.id IN ($id_list)
    ";
    $result = mysqli_query($link, $query);
    if (!$result) {
        $error_msg = $clientMessages[$lang]['database_query_failed'] . " " . mysqli_error($link);
        require_once 'stripe/StripeLogger.php';
        StripeLogger::log(StripeLogLevel::ERROR, "Database query failed in compare_coins", [
            'error' => mysqli_error($link),
            'query' => $query
        ]);
        http_response_code(500);
        echo json_encode(['error' => $error_msg]);
        return;
    }
    $coins = [];
    $languageNameColumn = "name_" . strtoupper($selectedLanguage);
    $languageNameColumn = mysqli_real_escape_string($link, $languageNameColumn);
    while ($coin = mysqli_fetch_assoc($result)) {
        $price = fetch_price_from_coingecko($coin['geckoid']);
        // Metric gruplarını ve skorları al
        $metricGroupsQuery = "SELECT id, name FROM metric_groups WHERE name != 'Vesting'";
        $metricGroupsResult = mysqli_query($link, $metricGroupsQuery);
        $scores = [];
        if ($metricGroupsResult && mysqli_num_rows($metricGroupsResult) > 0) {
            while ($group = mysqli_fetch_assoc($metricGroupsResult)) {
                $groupId = $group['id'];
                $groupName = $group['name'];
                // Her grup için alt metrik skorlarını al
                $metricSubgroupsQuery = "
                SELECT
                    mt.`$languageNameColumn` as name,
                    m.id,
                    COALESCE(cm.score, c.score) AS final_score
                FROM metric_subgroups m
                JOIN coin_scores c ON m.id = c.metric_subgroup
                LEFT JOIN coin_scores_manual cm ON c.geckoid = cm.geckoid AND cm.metric_subgroup = m.id
                JOIN metric_translations mt on m.id = mt.metric_id
                WHERE m.metric_group = $groupId AND c.geckoid = '{$coin['geckoid']}' AND m.isactive = 1
                ";
                $metricSubgroupsResult = mysqli_query($link, $metricSubgroupsQuery);
                $subScores = [];
                if ($metricSubgroupsResult && mysqli_num_rows($metricSubgroupsResult) > 0) {
                    while ($sub = mysqli_fetch_assoc($metricSubgroupsResult)) {
                        $subScores[] = [
                            'id' => $sub['id'],
                            'name' => $sub['name'],
                            'value' => isset($sub['final_score']) ? (float)$sub['final_score'] : null
                        ];
                    }
                }
                // Grup için toplam skoru al
                $totalQuery = "SELECT score FROM coin_group_scores_all WHERE metric_group = $groupId AND geckoid = '{$coin['geckoid']}'";
                $totalResult = mysqli_query($link, $totalQuery);
                $totalScore = null;
                if ($totalResult && mysqli_num_rows($totalResult) > 0) {
                    $row = mysqli_fetch_assoc($totalResult);
                    $totalScore = isset($row['score']) ? (float)$row['score'] : null;
                }
                $scores[] = [
                    'name' => $groupName,
                    'total' => $totalScore,
                    'subScores' => $subScores
                ];
            }
        }
        // Coin verilerini düzenle
        $coins[] = [
            'id' => $coin['geckoid'],
            'name' => $coin['name'],
            'symbol' => $coin['symbol'],
            'price' => $price,
            'priceChange24h' => floatval($coin['price_change_1d']),
            'priceChange7d' => floatval($coin['price_change_7d']),
            'priceChange30d' => floatval($coin['price_change_30d']),
            'marketCap' => floatval($coin['marketcap']),
            'volume24h' => floatval($coin['total_volume']),
            'category' => 'Cryptocurrency',
            'status' => 'Active',
            'logo' => $coin['image'],
            'image' => $coin['image'],
            'scores' => $scores  // Yeni eklenen metric skorları
        ];
    }
    echo json_encode([
        'success' => true,
        'coins' => $coins
    ], JSON_PRETTY_PRINT);
}

/* ============================================================================================== */
/*                                        Avatar Functions                                        */
/* ============================================================================================== */

/**
 * Update user avatar
 *
 * @param int $userId User ID
 * @param int $avatarId Avatar ID to set
 * @return void
 */
function update_user_avatar($userId, $avatarId)
{
    global $link, $selectedLanguage, $clientMessages;

    // Dil desteği için varsayılan dil kontrolü
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    // Avatar ID'yi validate et
    $avatarId = intval($avatarId);
    if ($avatarId <= 0) {
        $response = new ErrorResult('Invalid avatar ID.');
        $response->send(400);
        return;
    }

    // Kullanıcının avatar_id'sini güncelle
    $query = "UPDATE users SET avatar_id = ? WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);

    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "ii", $avatarId, $userId);

        if (mysqli_stmt_execute($stmt)) {
            $response = new SuccessResult([
                'message' => $clientMessages[$lang]['avatar_updated']
            ]);
            $response->send();
        } else {
            $response = new ErrorResult('Failed to update avatar.');
            $response->send(500);
        }

        mysqli_stmt_close($stmt);
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}

/**
 * Remove user avatar
 *
 * @param int $userId User ID
 * @return void
 */
function remove_user_avatar($userId)
{
    global $link, $selectedLanguage, $clientMessages;

    // Dil desteği için varsayılan dil kontrolü
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    // Kullanıcının avatar_id'sini NULL yap
    $query = "UPDATE users SET avatar_id = NULL WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);

    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $userId);

        if (mysqli_stmt_execute($stmt)) {
            $response = new SuccessResult([
                'message' => $clientMessages[$lang]['avatar_removed']
            ]);
            $response->send();
        } else {
            $response = new ErrorResult('Failed to remove avatar.');
            $response->send(500);
        }

        mysqli_stmt_close($stmt);
    } else {
        $response = new ErrorResult('Database error.');
        $response->send(500);
    }
}

/**
 * Get launchpad platforms data
 * Returns static data for now, will be connected to database later
 *
 * @return void JSON response is directly echoed
 */
function get_launchpad_platforms()
{
    // Static data for now - will be replaced with database query later
    $platforms = [
        [
            "id" => "pinksale",
            "name" => "PinkSale",
            "currentRoi" => 742,
            "athRoi" => 1623,
            "entryAmount" => "$100",
            "totalRaised" => "$934.2M",
            "blockchains" => ["BSC", "ETH", "Polygon"],
            "totalIDOs" => 1847,
            "socialScore" => 85,
            "coinScore" => 88,
            "activeIDOs" => 57
        ],
        [
            "id" => "dxsale",
            "name" => "DxSale",
            "currentRoi" => 523,
            "athRoi" => 1245,
            "entryAmount" => "$50",
            "totalRaised" => "$567.8M",
            "blockchains" => ["BSC", "ETH"],
            "totalIDOs" => 1234,
            "socialScore" => 78,
            "coinScore" => 82,
            "activeIDOs" => 43
        ],
        [
            "id" => "launchpool",
            "name" => "LaunchPool",
            "currentRoi" => 892,
            "athRoi" => 1876,
            "entryAmount" => "$200",
            "totalRaised" => "$1.2B",
            "blockchains" => ["ETH", "Polygon", "Arbitrum"],
            "totalIDOs" => 892,
            "socialScore" => 92,
            "coinScore" => 94,
            "activeIDOs" => 28
        ]
    ];

    $response = new SuccessResult($platforms);
    $response->send();
}

function get_documentation()
{
    global $link, $selectedLanguage;

    // Dil desteği için varsayılan dil kontrolü
    if (empty($selectedLanguage)) {
        $selectedLanguage = 'en';
    }

    try {
        // Join query to get documentation items with their translations
        $query = "SELECT
                    di.id,
                    di.slug,
                    di.parent_id,
                    di.order_index as `order`,
                    COALESCE(dt_selected.title, dt_en.title) as title,
                    COALESCE(dt_selected.content, dt_en.content) as content
                  FROM documentation_items di
                  LEFT JOIN documentation_translations dt_selected
                    ON di.id = dt_selected.item_id AND dt_selected.language_code = ?
                  LEFT JOIN documentation_translations dt_en
                    ON di.id = dt_en.item_id AND dt_en.language_code = 'en'
                  ORDER BY di.order_index ASC, COALESCE(dt_selected.title, dt_en.title) ASC";

        $stmt = mysqli_prepare($link, $query);
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "s", $selectedLanguage);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if (!$result) {
            throw new Exception("Database query failed: " . mysqli_error($link));
        }

        $allDocs = [];
        $docsByParent = [];

        // Organize documents by parent_id for hierarchical structure
        while ($row = mysqli_fetch_assoc($result)) {
            $doc = [
                "title" => $row['title'],
                "order" => (int)$row['order'],
                "children" => []
            ];

            // Add content only if it exists
            if (!empty($row['content'])) {
                $doc['content'] = $row['content'];
            }

            $allDocs[$row['id']] = $doc;

            if ($row['parent_id'] === null) {
                // Root level document
                $docsByParent['root'][] = $row['id'];
            } else {
                // Child document
                $docsByParent[$row['parent_id']][] = $row['id'];
            }
        }

        mysqli_stmt_close($stmt);

        // Build hierarchical structure recursively
        function buildHierarchy($parentId, &$allDocs, &$docsByParent) {
            $children = [];
            if (isset($docsByParent[$parentId])) {
                foreach ($docsByParent[$parentId] as $docId) {
                    $doc = $allDocs[$docId];
                    $doc['children'] = buildHierarchy($docId, $allDocs, $docsByParent);
                    $children[] = $doc;
                }
            }
            return $children;
        }

        // Build the final structure with slug keys
        $documentation = [];
        $query_slugs = "SELECT id, slug FROM documentation_items";
        $result_slugs = mysqli_query($link, $query_slugs);
        $slugMap = [];

        while ($row = mysqli_fetch_assoc($result_slugs)) {
            $slugMap[$row['id']] = $row['slug'];
        }

        // Build hierarchical structure with slug keys
        function buildHierarchyWithSlugs($parentId, &$allDocs, &$docsByParent, &$slugMap) {
            $children = [];
            if (isset($docsByParent[$parentId])) {
                foreach ($docsByParent[$parentId] as $docId) {
                    $slug = $slugMap[$docId];
                    $doc = $allDocs[$docId];
                    $childrenData = buildHierarchyWithSlugs($docId, $allDocs, $docsByParent, $slugMap);
                    if (!empty($childrenData)) {
                        $doc['children'] = $childrenData;
                    }
                    $children[$slug] = $doc;
                }
            }
            return $children;
        }

        // Get root level documents with their children
        $documentation = buildHierarchyWithSlugs('root', $allDocs, $docsByParent, $slugMap);

        $response = new SuccessResult($documentation);
        $response->send();

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "get_documentation failed", [
            'error' => $e->getMessage(),
            'language' => $selectedLanguage
        ]);

        $error = new ErrorResult("Failed to fetch documentation.");
        $error->send();
    }
}
