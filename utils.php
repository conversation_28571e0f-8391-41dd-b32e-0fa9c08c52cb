<?php

/**
 * Utility Functions for CoinScout Backend
 *
 * This file contains common utility functions used across the application.
 */
require_once 'vendor/autoload.php';
require_once('models/ResultModel.php');

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
/* ============================================================================================== */
/*                                     Authentication Utilities                                   */
/* ============================================================================================== */

/**
 * Validates a JWT token from the Authorization header
 *
 * @return object|null Decoded JWT payload or null if invalid
 */
function validate_jwt()
{
    $headers = apache_request_headers(); // Apache'yi kullanıyorsanız
    // Veya başka bir sunucu ortamında $_SERVER['HTTP_AUTHORIZATION'] da kullanılabilir.
    if (isset($headers['Authorization'])) {
        $authHeader = $headers['Authorization'];
        $token = str_replace('Bearer ', '', $authHeader);
        $secret_key = "48Scw74aAgf16wvAhhr85411A18w3";
        try {
            $decoded = JWT::decode($token, new Key($secret_key, 'HS256'));
            if ($decoded->exp < time()) {
                return null;
            }
            return $decoded;
        } catch (\Exception $e) {
            error_log("JWT validation error: " . $e->getMessage());
            return null;
        }
    }
    return null;
}
/**
 * Authenticates a user based on JWT token
 *
 * @return int|null User ID if authenticated, null otherwise
 */
function authenticate_user()
{
    $decoded = validate_jwt();
    if ($decoded === null) {
        // error_log("Token validation failed.");
        return null;
    }
    // $_SESSION['userId'] = $decoded->userId;
    return $decoded->userId;
}
/**
 * Authenticates a user and returns an error response if authentication fails
 *
 * @return int|false User ID if authenticated, false if authentication fails (after sending error response)
 */
function authenticate_user_or_error()
{
    $userId = authenticate_user();
    if ($userId === null) {
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
        $errorMessage = isset($clientMessages[$lang]['unauthorized_access'])
            ? $clientMessages[$lang]['unauthorized_access']
            : 'Unauthorized access. Please log in.';
        $response = new ErrorResult($errorMessage);
        $response->send(401);
        return false;
    }
    return $userId;
}
/**
 * Gets user permissions based on subscription level and Stripe subscription status
 *
 * @param int $userId User ID
 * @return array Array of permission strings
 */
function getUserPermissions($userId)
{
    global $link;

    // Include permissions file and StripeLogger
    require_once 'permissions.php';
    require_once 'stripe/StripeLogger.php';

    // Kullanıcı ID'si yoksa boş dizi döndür
    if (!$userId) {
        StripeLogger::log(StripeLogLevel::WARNING, "getUserPermissions called with null or empty userId");
        return getPermissionsByLevel('free');
    }

    // Get user's subscription level and validate with Stripe subscription status
    $query = "SELECT u.subscription_level, s.status as stripe_status, s.plan_name
              FROM users u
              LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                  AND s.status IN ('active', 'trialing')
              WHERE u.id = ?
              ORDER BY s.created_at DESC
              LIMIT 1";
    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        $error = mysqli_error($link);
        StripeLogger::log(StripeLogLevel::ERROR, "Failed to prepare statement in getUserPermissions", [
            'user_id' => $userId,
            'error' => $error
        ]);
        return getPermissionsByLevel('free'); // Default to free permissions
    }

    mysqli_stmt_bind_param($stmt, "i", $userId);

    if (!mysqli_stmt_execute($stmt)) {
        $error = mysqli_error($link);
        StripeLogger::log(StripeLogLevel::ERROR, "Failed to execute statement in getUserPermissions", [
            'user_id' => $userId,
            'error' => $error
        ]);
        return getPermissionsByLevel('free');
    }

    $result = mysqli_stmt_get_result($stmt);

    if (!$result) {
        $error = mysqli_error($link);
        StripeLogger::log(StripeLogLevel::ERROR, "Failed to get result in getUserPermissions", [
            'user_id' => $userId,
            'error' => $error
        ]);
        return getPermissionsByLevel('free');
    }

    $user = mysqli_fetch_assoc($result);

    if (!$user) {
        StripeLogger::log(StripeLogLevel::WARNING, "User not found", [
            'user_id' => $userId
        ]);
        return getPermissionsByLevel('free');
    }

    $userSubscriptionLevel = strtolower($user['subscription_level'] ?? 'free');
    $stripeStatus = $user['stripe_status'];
    $planName = $user['plan_name'];

    // Security check: If user has paid subscription level but no active Stripe subscription, downgrade to free
    if ($userSubscriptionLevel !== 'free' && empty($stripeStatus)) {
        StripeLogger::log(StripeLogLevel::WARNING, "SECURITY: User has paid subscription level but no active Stripe subscription - downgrading to free", [
            'user_id' => $userId,
            'user_subscription_level' => $userSubscriptionLevel,
            'stripe_status' => $stripeStatus,
            'plan_name' => $planName
        ]);

        // Update user subscription level to free for security
        $updateQuery = "UPDATE users SET subscription_level = 'free' WHERE id = ?";
        $updateStmt = mysqli_prepare($link, $updateQuery);
        if ($updateStmt) {
            mysqli_stmt_bind_param($updateStmt, "i", $userId);
            mysqli_stmt_execute($updateStmt);
            StripeLogger::log(StripeLogLevel::INFO, "User subscription level updated to free due to inactive Stripe subscription", [
                'user_id' => $userId
            ]);
        }

        return getPermissionsByLevel('free');
    }

    // Use free permissions if user has free subscription or no active Stripe subscription
    $finalLevel = ($userSubscriptionLevel === 'free' || empty($stripeStatus)) ? 'free' : $userSubscriptionLevel;

    StripeLogger::log(StripeLogLevel::INFO, "User permissions determined", [
        'user_id' => $userId,
        'user_subscription_level' => $userSubscriptionLevel,
        'stripe_status' => $stripeStatus,
        'final_level' => $finalLevel,
        'plan_name' => $planName
    ]);

    // Get permissions based on final subscription level
    $permissions = getPermissionsByLevel($finalLevel);

    // Add detailed logging
    StripeLogger::log(StripeLogLevel::DEBUG, "getPermissionsByLevel result", [
        'user_id' => $userId,
        'final_level' => $finalLevel,
        'result_type' => gettype($permissions),
        'is_array' => is_array($permissions),
        'count' => is_array($permissions) ? count($permissions) : 0
    ]);

    // If permissions is not an array, this indicates a critical error in permissions.php
    if (!is_array($permissions)) {
        StripeLogger::log(StripeLogLevel::ERROR, "getPermissionsByLevel returned non-array - critical permissions system error", [
            'user_id' => $userId,
            'final_level' => $finalLevel,
            'result_type' => gettype($permissions),
            'result_value' => $permissions
        ]);

        // Try one more time with free level as fallback
        if ($finalLevel !== 'free') {
            StripeLogger::log(StripeLogLevel::WARNING, "Attempting fallback to free permissions", [
                'user_id' => $userId,
                'original_level' => $finalLevel
            ]);
            $permissions = getPermissionsByLevel('free');
        }

        // If still not an array, return empty array and log critical error
        if (!is_array($permissions)) {
            StripeLogger::log(StripeLogLevel::CRITICAL, "Permissions system completely failed - returning empty permissions array", [
                'user_id' => $userId,
                'final_level' => $finalLevel,
                'fallback_result_type' => gettype($permissions)
            ]);
            $permissions = [];
        }
    }

    return $permissions;
}
/**
 * Gets SameSite value for authentication cookies
 *
 * For security tokens, we use Strict for all browsers to prevent CSRF attacks
 * and ensure tokens are only sent in first-party contexts.
 *
 * @return string SameSite value
 */
function getSameSiteValue()
{
    // Tüm tarayıcılar için Strict kullan - güvenlik için
    return 'Strict';
}

/**
 * Creates an authentication token (JWT)
 *
 * @param int $userId User ID
 * @return string JWT token
 */
function createAuthToken($userId)
{
    $secret_key = "48Scw74aAgf16wvAhhr85411A18w3";
    $permissions = getUserPermissions($userId);
    $token_duration_seconds = JWT_ACCESS_TOKEN_DURATION_MINUTES * 60;

    $payload = [
        'iss' => "coinscout.app",
        'aud' => "coinscout.app",
        'iat' => time(),
        'exp' => time() + $token_duration_seconds,
        'userId' => $userId,
        'permissions' => $permissions
    ];
    $jwt = JWT::encode($payload, $secret_key, 'HS256');

    $sameSite = getSameSiteValue();
    setcookie(
        "auth_token2",
        $jwt,
        [
            'expires' => time() + $token_duration_seconds,
            'path' => "/",
            'domain' => "coinscout.app",
            'secure' => true,
            'httponly' => false,
            'samesite' => $sameSite
        ]
    );
    return $jwt;
}
/**
 * Creates a refresh token
 *
 * @param int|null $expires_at Expiration timestamp (null for session-based)
 * @return string Refresh token
 */
function createRefreshToken($expires_at = null)
{
    $token = bin2hex(random_bytes(32));

    $sameSite = getSameSiteValue();
    $cookieOptions = [
        'path' => "/",
        'domain' => "coinscout.app",
        'secure' => true,
        'httponly' => true,
        'samesite' => $sameSite
    ];

    // Eğer expires_at null ise session-based cookie oluştur (tarayıcı kapanınca siler)
    if ($expires_at !== null) {
        $cookieOptions['expires'] = $expires_at;
    }
    // expires_at null ise expires ayarlanmaz, bu da session cookie yapar

    setcookie("refresh_token", $token, $cookieOptions);
    return $token;
}
/**
 * Stores a refresh token in the database
 *
 * @param int $userId User ID
 * @param string $token Refresh token
 * @param int $expires_at Expiration timestamp
 * @param string $user_ip User IP address
 * @return bool Success status
 */
function storeRefreshToken($userId, $token, $expires_at, $user_ip)
{
    global $link;
    $expires = date('Y-m-d H:i:s', $expires_at);
    $query = "INSERT INTO refresh_tokens (user_id, token, expires, created_at, created_by_ip) VALUES (?, ?, ?, NOW(), ?)";
    $stmt = mysqli_prepare($link, $query);
    if (!$stmt) {
        error_log("Failed to prepare statement for storing refresh token: " . mysqli_error($link));
        return false;
    }
    mysqli_stmt_bind_param($stmt, "isss", $userId, $token, $expires, $user_ip);
    $result = mysqli_stmt_execute($stmt);
    if (!$result) {
        error_log("Failed to store refresh token: " . mysqli_error($link));
    }
    return $result;
}
/* ============================================================================================== */
/*                                     Formatting Utilities                                       */
/* ============================================================================================== */
/**
 * Formats a score value with status
 *
 * @param float $score Score value
 * @return array Formatted score with status
 */
function format_score($score)
{
    return [
        'score' => (float)$score,
        'status' => getScoreStatus((float)$score)
    ];
}
/**
 * Gets status text based on score value
 *
 * @param float $score Score value
 * @return string Status text
 */
function getScoreStatus($score)
{
    if ($score >= 80) {
        return 'Excellent';
    } elseif ($score >= 60) {
        return 'Positive';
    } elseif ($score >= 40) {
        return 'Average';
    } elseif ($score >= 20) {
        return 'Weak';
    } else {
        return 'Critical';
    }
}
/**
 * Get metric group name by ID with language support
 *
 * @param int $groupId The metric group ID
 * @param string $language The language code (default: 'en')
 * @return string The localized metric group name
 */
function getMetricGroupNameById($groupId, $language = 'en')
{
    // Default English names
    $defaultNames = [
        1 => 'Tokenomics',
        2 => 'Vesting',
        3 => 'Security',
        4 => 'Social',
        5 => 'Market',
        6 => 'Insight'
    ];
    // Translations for different languages
    $translations = [
        'tr' => [
            1 => 'Tokenomik',
            2 => 'Kilitleme',
            3 => 'Güvenlik',
            4 => 'Sosyal',
            5 => 'Piyasa',
            6 => 'İçgörü'
        ],
        'es' => [
            1 => 'Tokenómica',
            2 => 'Bloqueo',
            3 => 'Seguridad',
            4 => 'Social',
            5 => 'Mercado',
            6 => 'Perspicacia'
        ],
        'fr' => [
            1 => 'Tokenomique',
            2 => 'Verrouillage',
            3 => 'Sécurité',
            4 => 'Social',
            5 => 'Marché',
            6 => 'Perspicacité'
        ],
        'de' => [
            1 => 'Tokenomik',
            2 => 'Sperrfrist',
            3 => 'Sicherheit',
            4 => 'Sozial',
            5 => 'Markt',
            6 => 'Einblick'
        ],
        'ar' => [
            1 => 'اقتصاد الرمز',
            2 => 'الاستحقاق',
            3 => 'الأمان',
            4 => 'اجتماعي',
            5 => 'السوق',
            6 => 'البصيرة'
        ],
        'hi' => [
            1 => 'टोकनॉमिक्स',
            2 => 'वेस्टिंग',
            3 => 'सुरक्षा',
            4 => 'सामाजिक',
            5 => 'बाजार',
            6 => 'अंतर्दृष्टि'
        ],
        'id' => [
            1 => 'Tokenomik',
            2 => 'Vesting',
            3 => 'Keamanan',
            4 => 'Sosial',
            5 => 'Pasar',
            6 => 'Wawasan'
        ],
        'it' => [
            1 => 'Tokenomics',
            2 => 'Vesting',
            3 => 'Sicurezza',
            4 => 'Sociale',
            5 => 'Mercato',
            6 => 'Approfondimento'
        ],
        'en' => [
            1 => 'Tokenomics',
            2 => 'Vesting',
            3 => 'Security',
            4 => 'Social',
            5 => 'Market',
            6 => 'Insight'
        ],
        'ja' => [
            1 => 'トークノミクス',
            2 => 'ベスティング',
            3 => 'セキュリティ',
            4 => 'ソーシャル',
            5 => '市場',
            6 => 'インサイト'
        ],
        'ko' => [
            1 => '토크노믹스',
            2 => '베스팅',
            3 => '보안',
            4 => '소셜',
            5 => '시장',
            6 => '인사이트'
        ],
        'pt' => [
            1 => 'Tokenômica',
            2 => 'Vesting',
            3 => 'Segurança',
            4 => 'Social',
            5 => 'Mercado',
            6 => 'Insight'
        ],
        'ru' => [
            1 => 'Токеномика',
            2 => 'Вестинг',
            3 => 'Безопасность',
            4 => 'Социальное',
            5 => 'Рынок',
            6 => 'Аналитика'
        ],
        'vi' => [
            1 => 'Tokenomics',
            2 => 'Vesting',
            3 => 'Bảo mật',
            4 => 'Xã hội',
            5 => 'Thị trường',
            6 => 'Hiểu biết'
        ],
        'zh' => [
            1 => '代币经济学',
            2 => '归属期',
            3 => '安全性',
            4 => '社交',
            5 => '市场',
            6 => '洞察'
        ]
    ];
    // Check if the language exists in translations
    if (isset($translations[$language])) {
        // Check if the group ID exists in the language translations
        if (isset($translations[$language][$groupId])) {
            return $translations[$language][$groupId];
        }
    }
    // Fallback to default English name
    return $defaultNames[$groupId] ?? 'Unknown';
}
/**
 * Get user ID from JWT token
 *
 * @return int|null User ID or null if not found
 */
function getUserIdFromJWT()
{
    // JWT token'ı al
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';

    if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        return null;
    }

    $jwt = $matches[1];

    // JWT token'ı doğrula ve kullanıcı ID'sini al
    try {
        $secret_key = "48Scw74aAgf16wvAhhr85411A18w3";
        $decoded = JWT::decode($jwt, new Key($secret_key, 'HS256'));
        return $decoded->userId ?? null;
    } catch (\Exception $e) {
        error_log("JWT validation error in getUserIdFromJWT: " . $e->getMessage());
        return null;
    }
}

/**
 * Check if user has a specific permission
 *
 * @param int $userId User ID
 * @param string $permission Permission to check
 * @return bool True if user has permission, false otherwise
 */
function hasUserPermission($userId, $permission)
{
    // Include StripeLogger
    require_once 'stripe/StripeLogger.php';

    if ($userId === null) {
        StripeLogger::log(StripeLogLevel::WARNING, "hasUserPermission: userId is null, returning false");
        return false;
    }

    $permissions = getUserPermissions($userId);

    // Permissions null ise boş dizi olarak kabul et
    if ($permissions === null) {
        StripeLogger::log(StripeLogLevel::WARNING, "getUserPermissions returned null for user ID: " . $userId);
        $permissions = [];
    }

    // Log the permission check details
    $hasPermission = in_array($permission, $permissions);
    StripeLogger::log(StripeLogLevel::DEBUG, "Permission check", [
        'user_id' => $userId,
        'permission' => $permission,
        'result' => $hasPermission,
        'permissions_is_array' => is_array($permissions),
        'permissions_count' => is_array($permissions) ? count($permissions) : 0
    ]);

    if (!$hasPermission) {
        if (is_array($permissions)) {
            StripeLogger::log(StripeLogLevel::DEBUG, "Available permissions", [
                'user_id' => $userId,
                'permissions' => implode(", ", $permissions)
            ]);
        } else {
            StripeLogger::log(StripeLogLevel::WARNING, "Permissions is not an array", [
                'user_id' => $userId,
                'permissions_type' => gettype($permissions)
            ]);
        }
    }

    return $hasPermission;
}

/**
 * Check if user can add more watchlists
 *
 * @param int $userId User ID
 * @return bool True if user can add more watchlists, false otherwise
 */
function canUserAddMoreWatchlists($userId)
{
    global $link;

    // Get user permissions
    $permissions = getUserPermissions($userId);

    // Count user's current watchlists
    $query = "SELECT COUNT(*) as count FROM watchlists WHERE user_id = ?";
    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        return false;
    }

    mysqli_stmt_bind_param($stmt, "i", $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $row = mysqli_fetch_assoc($result);
    $currentWatchlistCount = $row['count'];

    // Check if user can add more watchlists
    require_once 'permissions.php';
    return canAddMoreWatchlists($permissions, $currentWatchlistCount);
}

/**
 * Check if user can add more alerts
 *
 * @param int $userId User ID
 * @return bool True if user can add more alerts, false otherwise
 */
function canUserAddMoreAlerts($userId)
{
    global $link;

    // Get user permissions
    $permissions = getUserPermissions($userId);

    // Count user's current alerts
    $query = "SELECT COUNT(*) as count FROM user_alerts WHERE user_id = ? AND is_active = 1";
    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        return false;
    }

    mysqli_stmt_bind_param($stmt, "i", $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $row = mysqli_fetch_assoc($result);
    $currentAlertCount = $row['count'];

    // Check if user can add more alerts using permissions system
    require_once 'permissions.php';
    return canAddMoreAlerts($permissions, $currentAlertCount);
}

/**
 * Check if user is allowed to view a specific coin detail
 *
 * @param int|null $userId User ID (null for unauthenticated users)
 * @param int $coinId Coin ID to check
 * @return bool True if user can view this coin, false otherwise
 */
function isAllowedCoinForUser($userId, $coinId)
{
    global $link;

    // Include StripeLogger
    require_once 'stripe/StripeLogger.php';

    // Get user's subscription level and validate with Stripe subscription status
    $subscriptionLevel = 'free'; // Default to free
    if ($userId) {
        $query = "SELECT u.subscription_level, s.status as stripe_status
                  FROM users u
                  LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                      AND s.status IN ('active', 'trialing')
                  WHERE u.id = ?
                  ORDER BY s.created_at DESC
                  LIMIT 1";
        $stmt = mysqli_prepare($link, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "i", $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);
            $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
            $stripeStatus = $user['stripe_status'];

            // Security check: Only allow paid features if Stripe subscription is active
            $subscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';
        }
    }

    // Non-free users with active Stripe subscription can access all coins
    if ($subscriptionLevel !== 'free') {
        StripeLogger::log(StripeLogLevel::DEBUG, "Paid subscription user accessing coin detail", [
            'user_id' => $userId,
            'coin_id' => $coinId,
            'subscription_level' => $subscriptionLevel
        ]);
        return true;
    }

    // For free users, check if coin is in the first 20 coins from get_coins_list_v2
    // We need to replicate the same query logic as get_coins_list_v2
    $filters = ["isactive = 1"];
    $filter_string = implode(" AND ", $filters);
    $order_by = "marketcap DESC";

    $query = "SELECT id FROM client_coin_list
              WHERE $filter_string
              ORDER BY $order_by
              LIMIT 20";

    $rs = mysqli_query($link, $query);
    if ($rs) {
        $allowedCoinIds = [];
        while ($row = mysqli_fetch_assoc($rs)) {
            $allowedCoinIds[] = (int)$row['id'];
        }

        $isAllowed = in_array((int)$coinId, $allowedCoinIds);

        StripeLogger::log(StripeLogLevel::INFO, "Free user coin access check", [
            'user_id' => $userId,
            'coin_id' => $coinId,
            'is_allowed' => $isAllowed,
            'allowed_coins_count' => count($allowedCoinIds)
        ]);

        return $isAllowed;
    }

    StripeLogger::log(StripeLogLevel::ERROR, "Failed to check coin access for free user", [
        'user_id' => $userId,
        'coin_id' => $coinId
    ]);

    return false;
}

/**
 * Check if user is allowed to view a specific IDO detail
 *
 * @param int|null $userId User ID (null for unauthenticated users)
 * @param int $idoId IDO ID to check
 * @return bool True if user can view this IDO, false otherwise
 */
function isAllowedIdoForUser($userId, $idoId)
{
    global $link;

    // Include StripeLogger
    require_once 'stripe/StripeLogger.php';

    // Get user's subscription level and validate with Stripe subscription status
    $subscriptionLevel = 'free'; // Default to free
    if ($userId) {
        $query = "SELECT u.subscription_level, s.status as stripe_status
                  FROM users u
                  LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                      AND s.status IN ('active', 'trialing')
                  WHERE u.id = ?
                  ORDER BY s.created_at DESC
                  LIMIT 1";
        $stmt = mysqli_prepare($link, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "i", $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);
            $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
            $stripeStatus = $user['stripe_status'];

            // Security check: Only allow paid features if Stripe subscription is active
            $subscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';
        }
    }

    // Non-free users with active Stripe subscription can access all IDOs
    if ($subscriptionLevel !== 'free') {
        StripeLogger::log(StripeLogLevel::DEBUG, "Paid subscription user accessing IDO detail", [
            'user_id' => $userId,
            'ido_id' => $idoId,
            'subscription_level' => $subscriptionLevel
        ]);
        return true;
    }

    // For free users, check if IDO is in the first 10 IDOs from get_upcoming_idos_v2
    $query = "SELECT id FROM client_ico_coin_list
              ORDER BY total_score DESC
              LIMIT 10";

    $rs = mysqli_query($link, $query);
    if ($rs) {
        $allowedIdoIds = [];
        while ($row = mysqli_fetch_assoc($rs)) {
            $allowedIdoIds[] = (int)$row['id'];
        }

        $isAllowed = in_array((int)$idoId, $allowedIdoIds);

        StripeLogger::log(StripeLogLevel::INFO, "Free user IDO access check", [
            'user_id' => $userId,
            'ido_id' => $idoId,
            'is_allowed' => $isAllowed,
            'allowed_idos_count' => count($allowedIdoIds)
        ]);

        return $isAllowed;
    }

    StripeLogger::log(StripeLogLevel::ERROR, "Failed to check IDO access for free user", [
        'user_id' => $userId,
        'ido_id' => $idoId
    ]);

    return false;
}

/**
 * CORS handling for client requests
 */
function cors_client()
{
    // Allow from any origin
    if (isset($_SERVER['HTTP_ORIGIN'])) {
        $allowed_origins = [
            'http://localhost:3000',
            'http://localhost:5000',
            'http://localhost:5001',
            'https://coinscout.app',
            'https://www.coinscout.app',
            "https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev",
            "https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.repl.co",
            'https://977ddbbd-326c-46b1-947c-659c24f8ee4a-00-3snhwkdc8lpmi.picard.replit.dev:5000',
            'https://9898a5c7-6e4b-4e8b-b49c-1c3bc90f4306-00-2xt0byelho8qk.sisko.replit.dev:3000',
            "https://2716d30f-d804-432a-8819-2bffdf059c3b-00-1smz5kzeac1q5.worf.replit.dev"
        ];
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        if (preg_match('/^https:\/\/.*\.replit\.dev$/', $origin)) {
            $allowed_origins[] = $origin;
        }
        if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'], $allowed_origins)) {
            header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
            header("Access-Control-Allow-Credentials: true");
        }
        header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');    // cache for 1 day
    }
    // Access-Control headers are received during OPTIONS requests
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
            // may also be using PUT, PATCH, HEAD etc
            header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        }
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
            header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        }
        exit(0);
    }
}
